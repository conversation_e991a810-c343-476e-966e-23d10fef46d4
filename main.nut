// main.nut - OpenTTD GameScript spam systém

class PlayerTracker extends GSController
{
    function Spam()
    {
        GSLog.Info("Posílám zprávu do chatu: nazdar");
        GSGUI.ShowChatMessage(GSCompany.COMPANY_INVALID, "nazdar");
    }
    function Start()
    {
        // Spustí se při začátku hry
        while(true){
        	Spam();
        }
    }
}

// Registrace skriptu
function GetControllerClass() { return "SpamSystem"; }
